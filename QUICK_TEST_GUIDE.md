# 🚀 快速测试指南

## 立即测试修复效果

### 1. 重新加载扩展
1. 打开 `chrome://extensions/`
2. 找到"善思 Blinko智能收集器"
3. 点击刷新按钮 🔄

### 2. 测试自定义模型配置 ✅
1. 点击扩展图标打开侧边栏
2. 点击设置按钮（⚙️）
3. 在"AI服务配置"中选择任意服务商
4. 在"模型名称"下拉菜单中查看是否有"自定义模型"选项
5. 选择"自定义模型"
6. 确认"自定义模型名称"输入框显示
7. 输入您的模型名称（如：gpt-4、claude-3-sonnet等）

### 3. 测试API连接 ✅
1. 填写API密钥和基础地址
2. 点击"测试连接"按钮
3. 观察是否有测试结果显示：
   - 🔄 加载中：蓝色提示
   - ✅ 成功：绿色提示
   - ❌ 失败：红色提示

### 4. 测试选中文字功能 🔧
#### 方法一：自动检测
1. 在任意网页中选中一段文字（建议50字以上）
2. 打开侧边栏
3. 查看"选中内容"区域是否显示

#### 方法二：手动刷新（新增功能）
1. 在网页中选中文字
2. 打开侧边栏
3. 点击"选中内容"区域的刷新按钮（🔄）
4. 查看是否检测到选中文字

#### 方法三：直接测试
1. 打开侧边栏
2. 在网页中选中文字
3. 等待500ms自动检测
4. 或点击刷新按钮手动检测

### 5. 测试AI总结功能
1. 确认选中内容区域显示了文字
2. 点击"AI总结"按钮
3. 等待AI总结生成
4. 测试编辑功能（✏️按钮）
5. 测试移动功能（📄和💭按钮）

## 🔍 调试信息

### 打开控制台查看日志
按F12打开开发者工具，在Console中查看：

**选中文字相关日志**：
```
选中文本变化: "..."
Background收到选中文本变化: "..."
Side Panel更新选中文本: "..."
检测到选中文本storage变化
手动刷新选中文本
```

**自定义模型相关日志**：
```
更新自定义模型显示: {...}
模型选择变化: XXX
加载的AI模型配置: {...}
```

**API测试相关日志**：
```
开始测试AI连接
测试AI连接参数:
AI连接测试成功，响应: {...}
```

## 🎯 预期结果

### ✅ 应该正常工作的功能
- 所有AI服务商都有"自定义模型"选项
- 选择自定义模型后输入框正确显示
- API连接测试有明确的反馈
- 选中文字能被检测到（自动或手动）
- AI总结功能正常工作

### 🔧 如果功能异常

#### 自定义模型不显示
- 重新加载配置页面
- 检查是否选择了正确的服务商
- 查看控制台是否有错误

#### API测试无反馈
- 检查网络连接
- 确认API密钥格式正确
- 查看控制台错误信息

#### 选中文字不识别
1. **首先尝试手动刷新**：点击🔄按钮
2. **检查选中文字长度**：建议50字以上
3. **重新选中文字**：清除选择后重新选中
4. **查看控制台日志**：确认是否有错误
5. **重新打开侧边栏**：关闭后重新打开

## 📋 测试清单

请逐一测试并勾选：

- [ ] 自定义模型选项在所有服务商中可见
- [ ] 选择自定义模型后输入框显示
- [ ] 自定义模型名称可以输入和保存
- [ ] API连接测试按钮有反馈
- [ ] 测试成功时显示绿色提示
- [ ] 测试失败时显示红色错误
- [ ] 选中文字后侧边栏自动显示（或手动刷新后显示）
- [ ] 选中内容区域正确显示文字
- [ ] AI总结按钮可以生成总结
- [ ] 编辑功能正常工作
- [ ] 移动功能正常工作
- [ ] 控制台无严重错误

## 🆘 如果仍有问题

请提供以下信息：
1. 哪个功能不工作
2. 控制台的错误信息
3. 具体的操作步骤
4. 浏览器版本和扩展版本

我会立即进行进一步的修复！
