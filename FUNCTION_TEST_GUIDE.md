# 选中文字AI总结和自定义模型功能测试指南

## 测试前准备

### 1. 重新加载扩展
1. 打开 Chrome 扩展管理页面 (`chrome://extensions/`)
2. 找到"善思 Blinko智能收集器"
3. 点击刷新按钮重新加载扩展

### 2. 检查配置
1. 点击扩展图标打开侧边栏
2. 点击设置按钮（⚙️）进入配置页面
3. 确认AI服务配置正确

## 功能测试清单

### ✅ 选中文字AI总结功能测试

#### 1. 基础选中文本检测
- [ ] 在任意网页中选中一段文字（建议50字以上）
- [ ] 打开侧边栏，检查"选中内容"区域是否显示
- [ ] 确认选中的文字正确显示在文本框中

#### 2. AI总结生成
- [ ] 点击"AI总结"按钮
- [ ] 观察加载状态（按钮变为"生成中..."）
- [ ] 等待AI总结完成
- [ ] 检查总结内容是否合理

#### 3. 编辑功能
- [ ] 点击"编辑总结"按钮（✏️）
- [ ] 确认文本框变为可编辑状态
- [ ] 修改总结内容
- [ ] 点击"保存编辑"按钮（💾）
- [ ] 确认修改已保存

#### 4. 移动功能
- [ ] 点击"移动到原文摘要"按钮（📄）
- [ ] 检查内容是否移动到"原文摘要"区域
- [ ] 点击"移动到个人想法"按钮（💭）
- [ ] 检查内容是否移动到"个人想法"区域

#### 5. 清除功能
- [ ] 点击"清除选中内容"按钮（✕）
- [ ] 确认选中内容区域隐藏
- [ ] 重新选中文字，确认功能恢复

### ✅ 自定义模型配置功能测试

#### 1. 配置界面检查
- [ ] 打开配置页面
- [ ] 找到"AI服务配置"部分
- [ ] 检查"AI服务商"下拉菜单是否包含"自定义兼容服务"选项

#### 2. 自定义服务商配置
- [ ] 选择"自定义兼容服务"
- [ ] 确认"自定义模型名称"输入框显示
- [ ] 输入自定义模型名称（如：gpt-4、claude-3-sonnet等）
- [ ] 配置API地址和密钥
- [ ] 点击"保存设置"

#### 3. 硅基流动自定义模型
- [ ] 选择"硅基流动 (SiliconFlow)"
- [ ] 在模型选择中选择"其他模型..."
- [ ] 确认"自定义模型名称"输入框显示
- [ ] 输入硅基流动支持的模型名称
- [ ] 保存配置

#### 4. 模型使用测试
- [ ] 配置自定义模型后，测试页面AI总结
- [ ] 测试选中文字AI总结
- [ ] 检查生成的内容是否符合预期
- [ ] 观察控制台日志，确认使用了正确的模型

### ✅ 页面AI总结功能测试

#### 1. 基础功能
- [ ] 打开侧边栏
- [ ] 点击"AI总结"按钮
- [ ] 等待页面内容AI总结生成
- [ ] 检查总结质量

#### 2. 编辑功能
- [ ] 在"原文摘要"区域编辑AI生成的内容
- [ ] 确认编辑功能正常

### ✅ 综合功能测试

#### 1. 完整工作流程
- [ ] 打开一个内容丰富的网页
- [ ] 生成页面AI总结
- [ ] 选中部分文字生成选中文字总结
- [ ] 编辑两个总结内容
- [ ] 添加个人想法
- [ ] 添加标签
- [ ] 提交到Blinko

#### 2. 错误处理测试
- [ ] 在未配置AI的情况下尝试生成总结
- [ ] 选中过短的文字（少于10字）尝试总结
- [ ] 在网络断开时尝试生成总结
- [ ] 检查错误提示是否友好

## 调试信息

### 控制台日志检查
打开浏览器开发者工具（F12），在Console标签页中查看：

1. **选中文本相关日志**：
   - `选中文本变化: "..."`
   - `Background收到选中文本变化: "..."`
   - `Side Panel更新选中文本: "..."`

2. **AI总结相关日志**：
   - `开始生成选中文本AI总结，文本长度: XXX`
   - `Background开始生成选中文本AI总结`
   - `AI配置检查通过，服务商: XXX`

3. **自定义模型相关日志**：
   - `更新自定义模型显示: {...}`
   - `模型选择变化: XXX`
   - `加载的AI模型配置: {...}`

### 常见问题排查

#### 选中文字不显示
1. 检查是否正确选中了文字
2. 查看控制台是否有错误信息
3. 重新打开侧边栏

#### AI总结失败
1. 检查AI配置是否正确
2. 确认API密钥有效
3. 检查网络连接
4. 查看控制台错误信息

#### 自定义模型不显示
1. 确认选择了正确的服务商
2. 检查模型选择是否为"自定义"
3. 重新加载配置页面

## 测试完成标准

- [ ] 所有基础功能测试通过
- [ ] 选中文字AI总结功能完全正常
- [ ] 自定义模型配置和使用正常
- [ ] 错误处理友好
- [ ] 控制台无严重错误
- [ ] 用户体验流畅

## 反馈信息

测试完成后，请提供以下信息：
1. 哪些功能正常工作
2. 遇到的任何问题或错误
3. 控制台中的错误信息（如有）
4. 建议改进的地方
